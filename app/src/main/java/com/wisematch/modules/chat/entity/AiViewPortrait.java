package com.wisematch.modules.chat.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version AiChatInterview.java, v0.1 2025-06-20 20:12
 */
@Data
@TableName("ai_view_portrait")
@Schema(description = "AI人才画像")
public class AiViewPortrait implements Serializable {

    public static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    public String id;

    @Schema(description = "面试ID")
    public String roomId;

    @Schema(description = "用户ID")
    public String userId;

    @Schema(description = "申请岗位")
    public String position;

    @Schema(description = "申请岗位Id")
    public String positionId;

    @Schema(description = "人才画像")
    public String portrait;

    @Schema(description = "状态")
    public Integer status;

    @Schema(description = "入池审核状态：0-初始，1-审核通过，-1：审核不通过")
    public Integer verifyStatus;

    @Schema(description = "评分")
    public Integer score;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    public Date createTime;

    @Schema(description = "用户简介")
    public String brief;

}
