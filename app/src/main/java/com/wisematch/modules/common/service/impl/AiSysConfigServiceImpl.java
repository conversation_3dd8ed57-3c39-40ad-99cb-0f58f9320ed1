package com.wisematch.modules.common.service.impl;


import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.cache.GuavaCache;
import com.wisematch.modules.chat.enums.DictTypeEnum;
import com.wisematch.modules.common.entity.AiSysConfig;
import com.wisematch.modules.common.mapper.AiSysConfigMapper;
import com.wisematch.modules.common.service.AiSysConfigService;
import com.wisematch.modules.sys.constant.CacheConstants;
import jakarta.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class AiSysConfigServiceImpl extends ServiceImpl<AiSysConfigMapper, AiSysConfig> implements AiSysConfigService {
    @Override
    public AiSysConfig getByKey(String name) {
        AiSysConfig aiSysConfig = this.baseMapper.selectOne(new QueryWrapper<AiSysConfig>().lambda().eq(AiSysConfig::getConfigKey, name));
        return aiSysConfig;
    }


    /**
     * 获取验证码开关
     *
     * @return true开启，false关闭
     */
    @Override
    public boolean selectCaptchaEnabled() {
        String captchaEnabled = selectConfigByKey("sys.account.captchaEnabled");
        if (StringUtils.isEmpty(captchaEnabled)) {
            return true;
        }
        return Convert.toBool(captchaEnabled);
    }

    @Autowired
    private GuavaCache guavaCache;
    @Resource
    private AiSysConfigMapper aiconfigMapper;

    /**
     * 根据键名查询参数配置信息
     *
     * @param configKey 参数key
     * @return 参数键值
     */
    @Override
    public String selectConfigByKey(String configKey) {
        String configValue = (String) guavaCache.getObj(getCacheKey(configKey));
        if (StringUtils.isNotBlank(configValue)) {
            return configValue;
        }
        AiSysConfig config = new AiSysConfig();
        config.setConfigKey(configKey);
        AiSysConfig retConfig = aiconfigMapper.selectConfig(config);
        if (null != retConfig) {
            guavaCache.putObj(getCacheKey(configKey), retConfig.getConfigValue());
            return retConfig.getConfigValue();
        }
        return StringUtils.EMPTY;
    }

    @Override
    public Boolean selectBoolConfigByKey(String configKey) {
        String config = selectConfigByKey(configKey);
        if(StringUtils.isNotBlank(config)){
            return Boolean.getBoolean(config);
        }
        return null;
    }

    @Override
    public JSONObject getInterviewConfig() {
        return JSONObject.parseObject(this.selectConfigByKey("view_room_param"));
    }

    @Override
    public JSONObject getSysConfig() {
        return JSONObject.parseObject(this.selectConfigByKey("sys_config"));
    }

    @Override
    public Map<String,String> selectConfigByKeyMulti(DictTypeEnum[] dictTypeEnum) {
        Map<String,String> map = new HashMap<>();
        for (DictTypeEnum typeEnum : dictTypeEnum) {
            String key = typeEnum.name();
            String value = selectConfigByKey(typeEnum.name());
            map.put(key,value);
        }
        return map;
    }

    /**
     * 设置cache key
     *
     * @param configKey 参数键
     * @return 缓存键key
     */
    private String getCacheKey(String configKey) {
        return CacheConstants.SYS_CONFIG_KEY + configKey;
    }
}

