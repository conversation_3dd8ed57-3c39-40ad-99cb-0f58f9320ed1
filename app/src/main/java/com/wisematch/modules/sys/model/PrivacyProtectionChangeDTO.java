package com.wisematch.modules.sys.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

import static com.wisematch.modules.chat.enums.WiserConstant.PRIVATE_NOT_PROTECT;

@Data
@Schema(description = "修改工作状态")
public class PrivacyProtectionChangeDTO implements Serializable {

    @NotNull
    @Schema(description = "姓名是否隐私保护0：不保护，1保护", requiredMode = Schema.RequiredMode.REQUIRED)
    public Integer nameProtection = PRIVATE_NOT_PROTECT;

    @NotNull
    @Schema(description = "头像是否隐私保护0：不保护，1保护", requiredMode = Schema.RequiredMode.REQUIRED)
    public Integer photoProtection = PRIVATE_NOT_PROTECT;

    @NotNull
    @Schema(description = "视频是否隐私保护0：不保护，1保护", requiredMode = Schema.RequiredMode.REQUIRED)
    public Integer videoProtection = PRIVATE_NOT_PROTECT;

    @NotNull
    @Schema(description = "手机号是否隐私保护0：不保护，1保护", requiredMode = Schema.RequiredMode.REQUIRED)
    public Integer phoneProtection = PRIVATE_NOT_PROTECT;

    @NotNull
    @Schema(description = "微信是否隐私保护0：不保护，1保护", requiredMode = Schema.RequiredMode.REQUIRED)
    public Integer vxProtection = PRIVATE_NOT_PROTECT;

    public static PrivacyProtectionChangeDTO getByJson(JSONObject privacyProtection){
        if(null != privacyProtection){
            return privacyProtection.toJavaObject(PrivacyProtectionChangeDTO.class);
        }
        return new PrivacyProtectionChangeDTO();
    }

    public static PrivacyProtectionChangeDTO getByString(String privacyProtection){
        JSONObject jsonObject = JSON.parseObject(privacyProtection);
        return getByJson(jsonObject);
    }

}
