package com.wisematch.modules.invitation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.invitation.entity.AiInvitation;
import com.wisematch.modules.invitation.model.GeneratePilotEnterpriseUserDTO;


/**
 * AI智能体服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:43:39
 */
public interface AiInvitationService extends IService<AiInvitation> {

    void generatePilotEnterpriseUser(GeneratePilotEnterpriseUserDTO generatePilotEnterpriseUserDTO);

    boolean consumeInvitation(String invitationId, String userId);

    AiInvitation getInviteCode(String invitationId, String userId);

    boolean consumeInvitation(AiInvitation one);

    AiInvitation judgeInviteCode(String invitationCode, String userId);

    Boolean ifInvite();
}
