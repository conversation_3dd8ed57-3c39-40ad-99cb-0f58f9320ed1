package com.wisematch.modules.invitation.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.modules.invitation.entity.AiInvitationRecord;
import com.wisematch.modules.invitation.mapper.AiInvitationRecordMapper;
import com.wisematch.modules.invitation.service.AiInvitationRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * 智能体池
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:46:09
 */
@Service
@Slf4j
public class AiInvitationRecordServiceImpl extends ServiceImpl<AiInvitationRecordMapper, AiInvitationRecord> implements AiInvitationRecordService {



}
