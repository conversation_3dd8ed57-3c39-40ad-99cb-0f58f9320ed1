package com.wisematch.config.client;

import com.wisematch.common.exception.RRException;
import com.wisematch.common.utils.AgentsResult;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.JdkClientHttpRequestFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestClient;
import org.springframework.web.client.support.RestClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;
import org.springframework.web.util.DefaultUriBuilderFactory;

import java.net.http.HttpClient;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.List;
import java.util.Map;


public class AgentsHttpClientServiceFactory {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static <T> T createHttpService(Class<T> serviceClass, String url, HttpClient httpClient, DefaultUriBuilderFactory.EncodingMode encodingMode, HttpHeaders headers, Duration timeout, List<? extends ClientHttpRequestInterceptor> clientHttpRequestInterceptors) {
        JdkClientHttpRequestFactory requestFactory = new JdkClientHttpRequestFactory(httpClient);
        requestFactory.setReadTimeout(timeout);

        DefaultUriBuilderFactory uriBuilderFactory;
        if (StringUtils.hasLength(url)) {
            uriBuilderFactory = new DefaultUriBuilderFactory(url);
        } else {
            uriBuilderFactory = new DefaultUriBuilderFactory();
        }

        if (encodingMode != null) {
            uriBuilderFactory.setEncodingMode(encodingMode);
        }

        RestClient.Builder clientBuilder = RestClient.builder()
                .baseUrl(url)
                .uriBuilderFactory(uriBuilderFactory)
                .requestFactory(requestFactory)
                .requestInterceptor((request, body, execution) -> {
                    // 打印请求详情
                    System.out.println("=== 请求详情 ===");
                    System.out.println("URL: " + request.getURI());
                    System.out.println("Method: " + request.getMethod());
                    System.out.println("Headers: " + request.getHeaders());

                    if (body != null && body.length > 0) {
                        String bodyContent = new String(body, StandardCharsets.UTF_8);
                        System.out.println("Request Body: " + bodyContent);
                    }
                    System.out.println("================");

                    // 执行请求并打印响应
                    var response = execution.execute(request, body);

                    System.out.println("=== 响应详情 ===");
                    System.out.println("Status: " + response.getStatusCode());
                    System.out.println("Response Headers: " + response.getHeaders());
                    System.out.println("================");

                    return response;
                })
                .defaultStatusHandler(HttpStatusCode::isError, (request, response) -> {
                    String responseBody = new String(response.getBody().readAllBytes(), StandardCharsets.UTF_8);
                    // 尝试解析为 Result 对象
                    AgentsResult<?> result = objectMapper.readValue(responseBody, AgentsResult.class);
                    // 如果解析成功，直接抛出包含原始错误消息的异常
                    throw new RRException(result.getMessage());
                });

        if (!CollectionUtils.isEmpty(clientHttpRequestInterceptors)) {
            clientBuilder.requestInterceptors(interceptors -> interceptors.addAll(clientHttpRequestInterceptors));
        }

        if (!CollectionUtils.isEmpty(headers)) {
            clientBuilder.defaultHeaders(httpHeaders -> {
                for (Map.Entry<String, List<String>> entry : headers.entrySet()) {
                    httpHeaders.addAll(entry.getKey(), entry.getValue());
                }
            });
        }

        RestClient client = clientBuilder.build();
        RestClientAdapter restClientAdapter = RestClientAdapter.create(client);
        HttpServiceProxyFactory factory = HttpServiceProxyFactory.builderFor(restClientAdapter).build();
        return factory.createClient(serviceClass);
    }

}
