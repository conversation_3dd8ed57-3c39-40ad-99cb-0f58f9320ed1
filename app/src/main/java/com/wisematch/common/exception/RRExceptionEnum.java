package com.wisematch.common.exception;


public enum RRExceptionEnum {

    FAIL(201, "失败"),
    SERVICE_ERROR(203, "服务异常"),
    DATA_ERROR(204, "数据异常"),
    ILLEGAL_REQUEST(205, "非法请求"),
    REPEAT_SUBMIT(206, "重复提交"),
    DELETE_ERROR(207, "请先删除子集"),

    USER_LOGIN_AUTH(401, "请重新登录"),
    ADMIN_ACCOUNT_NOT_EXIST_ERROR(306, "账号不存在"),
    ADMIN_ACCOUNT_ERROR(307, "用户名或密码错误"),
    ADMIN_ACCOUNT_DISABLED_ERROR(308, "该用户已被禁用"),
    ADMIN_ACCESS_FORBIDDEN(309, "无访问权限"),
    USER_DELETED(310, "用户已注销"),
    PHONE_NUMBER_NOT_FOND(312, "用户无电话号"),
    VX_NUMBER_NOT_FOND(313, "用户无微信号"),
    INVITATION_VALID(314, "邀请码无效"),

    APP_LOGIN_AUTH(501, "请登录后使用"),
    APP_LOGIN_PHONE_EMPTY(502, "手机号码为空"),
    APP_LOGIN_CODE_EMPTY(503, "验证码为空"),
    APP_SEND_SMS_TOO_OFTEN(504, "验证码发送过于频繁"),
    APP_LOGIN_CODE_EXPIRED(505, "验证码已过期"),
    APP_LOGIN_CODE_ERROR(506, "验证码错误"),
    PHONE_NOT_EXIST_ERROR(507, "手机号不存在"),
    LOGIN_TYPE_NOT_SUPPORT(508, "登录渠道不支持"),

    // 1xx: 通用错误
    UNKNOWN_ERROR(1000, "网络走神了,请稍后重试"),
    PARAM_ERROR(1001, "参数校验失败"),
    NULL_POINTER(1002, "空指针异常"),
    ILLEGAL_ARGUMENT(1003, "非法参数"),
    TYPE_CAST_ERROR(1004, "类型转换异常"),
    IO_ERROR(1005, "输入输出异常"),
    METHOD_NOT_SUPPORTED(1006, "请求方式不支持"),
    JSON_PARSE_ERROR(1007, "JSON解析异常"),
    DATA_CONVERSION_ERROR(1008, "数据转换异常"),

    // 2xx: 权限与认证相关
    UNAUTHORIZED(2001, "请登录后使用"),
    TOKEN_EXPIRED(2003, "Token已过期"),
    TOKEN_INVALID(2004, "Token无效"),
    LOGIN_FAILED(2005, "登录失败"),
    ACCOUNT_LOCKED(2006, "账号被锁定"),
    AUTHENTICATION_FAILED(2007, "认证失败"),
    ID_CARD_IS_USED(2007, "身份证已经被其他账号认证"),


    // 3xx: 业务逻辑错误
    RESOURCE_NOT_FOUND(3001, "资源未找到"),
    OPERATION_NOT_ALLOWED(3002, "操作不被允许"),
    DUPLICATE_OPERATION(3003, "请勿重复操作"),
    DATA_EXISTS(3004, "数据已存在"),
    DATA_NOT_EXISTS(3005, "数据不存在"),
    DATA_INCONSISTENT(3006, "数据不一致"),
    ONE_RESUME_RESTRICT(3007, "只允许公开一个附件简历"),
    RESUME_ERROR(3009, "解析简历失败"),
    ORG_NOT_FOUND(3010, "请上传营业执照"),

    // 4xx: 数据库或第三方系统异常
    DATABASE_ERROR(4001, "数据库异常"),
    SQL_SYNTAX_ERROR(4002, "SQL语法错误"),
    DUPLICATE_PRIMARY_KEY(4003, "已有重复数据，请勿重复提交"),

    REDIS_ERROR(4003, "Redis操作异常"),
    MQ_ERROR(4004, "消息队列异常"),
    SERVICE_UNAVAILABLE(4005, "服务不可用"),
    EXTERNAL_API_ERROR(4006, "外部接口调用失败"),
    BUSINESS_LICENSE_ERROR(4007, "请上传正确的营业执照"),

    // 5xx: 文件/上传/下载等
    FILE_UPLOAD_FAIL(5001, "文件上传失败"),
    FILE_DOWNLOAD_FAIL(5002, "文件下载失败"),
    FILE_NOT_FOUND(5003, "文件不存在"),
    UNSUPPORTED_FILE_TYPE(5004, "不支持的文件类型"),

    // 6xx: 系统配置类
    CONFIG_ERROR(6001, "系统配置错误"),
    CONFIG_NOT_FOUND(6002, "配置未找到"),

    // 7xx: AI/算法服务相关
    AI_SERVICE_ERROR(7001, "AI服务异常"),
    AI_MODEL_ERROR(7002, "AI模型异常"),
    AGENT_ERROR(7003, "意图识别agent异常"),

    // 9xx: 其他
    TOO_MANY_REQUESTS(9001, "请求过于频繁"),
    HTTP_ERROR(9001, "远程服务请求出错"),
    TIMEOUT(9002, "请求超时"),


    // 10000以上的市需要去进行业务跳转的

    ORG_NOT_VERIFY(10003, "未进行企业认证"),
    NOT_CERTIFICATED(10002, "请进行实名认证"),
    PLEASE_UPLOAD_RESUME(10001, "请上传简历");



    private final int code;
    private final String message;

    RRExceptionEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() { return code; }

    public String getMessage() { return message; }

    public static RRExceptionEnum fromCode(int code) {
        for (RRExceptionEnum e : values()) {
            if (e.code == code) {
                return e;
            }
        }
        return UNKNOWN_ERROR;
    }
}


