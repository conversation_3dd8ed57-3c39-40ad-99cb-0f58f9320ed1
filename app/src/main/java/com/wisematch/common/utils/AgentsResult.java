package com.wisematch.common.utils;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wisematch.common.exception.RRException;
import lombok.Data;

import java.util.function.Supplier;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AgentsResult<T> {

    private Boolean success;

    private T data;

    private String message;

    private Long processingTime;

    public static <T> T handleResult(Supplier<AgentsResult<T>> func) {
        AgentsResult<T> result = func.get();
        if (Boolean.TRUE.equals(result.success)) {
            throw new RRException(result.message);
        }
        return result.data;
    }
}
