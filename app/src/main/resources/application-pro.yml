# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: PkfD6tobvLq6x3n4dsmIqbJRHz0zxYvHk4ZyqzhH4e4LR/AVhx+JuxeOJa6xRel7WZxCQS4wj9yCknLHDDuQ0w==
  # 令牌有效期（默认24*10小时）
  expireTime: 14400
server:
  error:
    whitelabel:
      enabled: false
    include-message: never
spring:
  flyway:
    # 开启 flyway
    enabled: true
    # 检测迁移脚本的路径是否存在，如不存在，则抛出异常
    check-location: true
    # 是否禁用数据库清理
    clean-disabled: true
    # SQL 迁移的编码
    encoding: UTF-8
    # 迁移脚本的位置，默认db/migration.
    locations: classpath:db/migration
    # 版本记录信息表
    table: tb_flyway_history
    # 如果不手动创建元数据表，则需要进行以下配置，用于自动创建
    baseline-on-migrate: true
    validate-on-migrate: true
  servlet:
    multipart:
      enabled: true               #  开启文件上传（默认就是 true，但你可能禁用了）
      max-file-size: 50MB        #  限制单个文件最大 50MB
      max-request-size: 100MB    #  限制总请求大小
  # 使用Spring @Cacheable注解失效时间
  cache:
    # 过期时间 单位秒
    time-to-live: 60 #秒
  data:
    redis:
      host: r-bp11btxn3dcsu769xypd.redis.rds.aliyuncs.com
      port: 6379
      password: Qlqy@888

  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: Qinglqy
    password: Qinglqy@2025
    url: ****************************************************************************************************************************************************************************************
  ai:
    dashscope:
      api-key: sk-b37b4bed73ec4762bdb8c067783a8670
      chat:
        options:
          model: qwen3-32b
    #    deepseek:
    #      api-key: ***********************************
    #      base-url: "https://api.deepseek.com"
    #      chat:
    #        options:
    #          model: deepseek-chat
    #      embedding:
    #        enabled: false
    #    memory:
    #      redis:
    #        host: redis-shzlaank1gxu5877a.redis.volces.com
    #        port: 6379
    #        password:
    #        timeout: 5000
    #    chat:
    #      memory:
    #        repository:
    #          jdbc:
    #            mysql:
    #              jdbc-url: ***********************************************************************************************************************************************************************************
    #              username: jingyouxindong
    #              password: Jingyxd@
    #              driver-class-name: com.mysql.cj.jdbc.Driver
    #              enabled: true
    interview:
      appid: 6854ffb2f69319017564c1f7
      app-key: c3356f4951904971afb729b77d192b76
      asr:
        appid: 8489833979
        token: yLpOIpzbGuTKwNogUhNkqXJOtuVxrHIl
      tts:
        appid: 7604591089
        token: M_DO1jpElFo63yeP6roC0vc18_J6lYxq
      llm:
        url: https://wisematch.com.cn/ai/chat/stream
  volc:
    access-key-id: AKLTNGUwMzYyODI0ZGQ3NDYzYmEzYmRjMzI5NGYwZmI1OTM
    secret-access-key: WkRnNE4yVXlaalUzT1dFMU5ESXhabUk1TXpNME1XUmxaVEk1WkROaE9USQ==


message:
  phone:
    aliyun:
      endpoint: 'dysmsapi.aliyuncs.com'
      region-id: 'cn-hangzhou'
      access-key-id: 'LTAI5t79qcqsk2cbmHTjJQNf'
      access-key-secret: '******************************'
      sign-name: '杭州青麟栖云科技'
      verify-template-code: 'SMS_321805530'

aliyun:
  oss:
    endpoint: oss-cn-hangzhou.aliyuncs.com
    access-key-id: LTAI5tPcN5kZ8RFWswxRt5oF
    access-key-secret: ******************************
    uri: https://wise-match.oss-cn-hangzhou.aliyuncs.com
    bucket-name: wise-match
  embedding:
    base-url: https://dashscope.aliyuncs.com/compatible-mode/v1
    key: sk-b37b4bed73ec4762bdb8c067783a8670
  milvus:
    endpoint: http://c-930eadcb57607ece-internal.milvus.aliyuncs.com
    username: root
    password: Qlqy@2025
    port: 19530

wsflash:
  android:
    appId: 8J94qGne
    appKey: hDWSH1I4
  ios:
    appId: xA91vThQ
    appKey: VQbOhILI
  url: https://wsflash.253.com/open/flashsdk/mobile-query


sys:
  log:
    enableLog: true
#
## Mybatis log plugin 插件输出日志配置
#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


xxl:
  job:
    admin:
      addresses: http://***********:8080/xxl-job-admin
      accessToken: A0NzYwMCIsImxvZ2luX3Vz
      executor:
        appname: wise-match-executor    # 执行器名称
        port: 9999
        ip: ************** # 这个只写了一台，需要去admin线上配置其他的节点

agents-server:
  report:
    url: https://alb-mnz304dllhvgi6toyw.cn-hangzhou.alb.aliyuncsslb.com/report